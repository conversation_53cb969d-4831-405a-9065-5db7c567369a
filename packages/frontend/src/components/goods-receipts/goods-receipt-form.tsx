'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useImmer } from 'use-immer';
import { Plus, Trash2, Package, FileText, Calendar, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { SupplierSelector } from '@/components/ui/supplier-selector';
import { ProductSelector } from '@/components/ui/product-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { GoodsReceiptFormData, GoodsReceiptFormItem, CreateGoodsReceiptDto } from '@/types/goods-receipt';
import { PurchaseOrder, PurchaseOrderStatus } from '@/types/purchase-order';
import { usePurchaseOrders } from '@/hooks/usePurchaseOrders';
import { formatCurrency } from '@/lib/utils';

// Constants for Select options
const NO_PURCHASE_ORDER = '__NO_PURCHASE_ORDER__';
import { toast } from 'sonner';

interface GoodsReceiptFormProps {
  initialData?: Partial<GoodsReceiptFormData>;
  onSubmit: (data: CreateGoodsReceiptDto) => Promise<void>;
  isSubmitting?: boolean;
  mode?: 'create' | 'edit';
}

const goodsReceiptItemSchema = z.object({
  productId: z.string().min(1, 'Produk harus dipilih'),
  unitId: z.string().min(1, 'Unit harus dipilih'),
  quantityOrdered: z.number().optional(),
  quantityReceived: z.number().min(1, 'Jumlah diterima harus lebih dari 0'),
  unitPrice: z.number().min(0, 'Harga unit tidak boleh negatif'),
  batchNumber: z.string().optional(),
  expiryDate: z.string().optional(),
  manufacturingDate: z.string().optional(),
  storageLocation: z.string().optional(),
  conditionOnReceipt: z.string().optional(),
  notes: z.string().optional(),
});

const goodsReceiptFormSchema = z.object({
  purchaseOrderId: z.string().optional(),
  supplierId: z.string().min(1, 'Supplier harus dipilih'),
  receiptDate: z.string().min(1, 'Tanggal penerimaan harus diisi'),
  deliveryDate: z.string().optional(),
  invoiceNumber: z.string().optional(),
  deliveryNote: z.string().optional(),
  deliveredBy: z.string().optional(),
  receivedBy: z.string().optional(),
  deliveryCondition: z.string().optional(),
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
  items: z.array(goodsReceiptItemSchema).min(1, 'Minimal harus ada satu item'),
});

type GoodsReceiptFormValues = z.infer<typeof goodsReceiptFormSchema>;

export function GoodsReceiptForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  mode = 'create',
}: GoodsReceiptFormProps) {
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<PurchaseOrder | null>(null);
  const [totalAmount, setTotalAmount] = useImmer(0);

  // Get purchase orders for the selected supplier
  const { data: purchaseOrdersResponse } = usePurchaseOrders({
    supplierId: initialData?.supplierId,
    status: PurchaseOrderStatus.APPROVED,
    limit: 100,
  });

  const form = useForm<GoodsReceiptFormValues>({
    resolver: zodResolver(goodsReceiptFormSchema),
    defaultValues: {
      purchaseOrderId: initialData?.purchaseOrderId || '',
      supplierId: initialData?.supplierId || '',
      receiptDate: initialData?.receiptDate || new Date().toISOString().split('T')[0],
      deliveryDate: initialData?.deliveryDate || '',
      invoiceNumber: initialData?.invoiceNumber || '',
      deliveryNote: initialData?.deliveryNote || '',
      deliveredBy: initialData?.deliveredBy || '',
      receivedBy: initialData?.receivedBy || '',
      deliveryCondition: initialData?.deliveryCondition || '',
      notes: initialData?.notes || '',
      internalNotes: initialData?.internalNotes || '',
      items: initialData?.items || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Calculate total amount when items change
  useEffect(() => {
    const items = form.watch('items');
    const total = items.reduce((sum, item) => {
      return sum + (item.quantityReceived * item.unitPrice);
    }, 0);
    setTotalAmount(total);
  }, [form.watch('items'), setTotalAmount]);

  // Load purchase order items when PO is selected
  const handlePurchaseOrderChange = useCallback((purchaseOrderId: string) => {
    if (!purchaseOrderId) {
      setSelectedPurchaseOrder(null);
      return;
    }

    const purchaseOrder = purchaseOrdersResponse?.data.find(po => po.id === purchaseOrderId);
    if (purchaseOrder) {
      setSelectedPurchaseOrder(purchaseOrder);

      // Clear existing items and add PO items
      form.setValue('items', []);

      purchaseOrder.items.forEach((poItem) => {
        append({
          productId: poItem.productId,
          unitId: poItem.unitId,
          quantityOrdered: poItem.quantityOrdered,
          quantityReceived: 0, // User needs to fill this
          unitPrice: poItem.unitPrice,
          batchNumber: '',
          expiryDate: '',
          manufacturingDate: '',
          storageLocation: '',
          conditionOnReceipt: 'good',
          notes: '',
        });
      });
    }
  }, [purchaseOrdersResponse, append, form]);

  const handleAddItem = useCallback(() => {
    append({
      productId: '',
      unitId: '',
      quantityOrdered: undefined,
      quantityReceived: 0,
      unitPrice: 0,
      batchNumber: '',
      expiryDate: '',
      manufacturingDate: '',
      storageLocation: '',
      conditionOnReceipt: 'good',
      notes: '',
    });
  }, [append]);

  const handleRemoveItem = useCallback((index: number) => {
    remove(index);
  }, [remove]);

  const handleFormSubmit = async (data: GoodsReceiptFormValues) => {
    try {
      const submitData: CreateGoodsReceiptDto = {
        ...data,
        items: data.items.map(item => ({
          productId: item.productId,
          unitId: item.unitId,
          quantityOrdered: item.quantityOrdered,
          quantityReceived: item.quantityReceived,
          unitPrice: item.unitPrice,
          batchNumber: item.batchNumber || undefined,
          expiryDate: item.expiryDate || undefined,
          manufacturingDate: item.manufacturingDate || undefined,
          storageLocation: item.storageLocation || undefined,
          conditionOnReceipt: item.conditionOnReceipt || undefined,
          notes: item.notes || undefined,
        })),
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Error submitting goods receipt:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Header Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Informasi Penerimaan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier Selection */}
              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier *</FormLabel>
                    <FormControl>
                      <SupplierSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih supplier..."
                      />
                    </FormControl>
                    <FormDescription>&nbsp;</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Purchase Order Selection */}
              <FormField
                control={form.control}
                name="purchaseOrderId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Purchase Order</FormLabel>
                    <Select
                      value={field.value || NO_PURCHASE_ORDER}
                      onValueChange={(value) => {
                        const actualValue = value === NO_PURCHASE_ORDER ? '' : value;
                        field.onChange(actualValue);
                        handlePurchaseOrderChange(actualValue);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih purchase order (opsional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={NO_PURCHASE_ORDER}>Tanpa Purchase Order</SelectItem>
                        {purchaseOrdersResponse?.data.map((po) => (
                          <SelectItem key={po.id} value={po.id}>
                            {po.orderNumber} - {formatCurrency(po.totalAmount)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Pilih purchase order untuk mengisi item secara otomatis
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Receipt Date */}
              <FormField
                control={form.control}
                name="receiptDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Penerimaan *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Delivery Date */}
              <FormField
                control={form.control}
                name="deliveryDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tanggal Pengiriman</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Invoice Number */}
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Invoice</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nomor invoice..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Delivery Note */}
              <FormField
                control={form.control}
                name="deliveryNote"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Surat Jalan</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nomor surat jalan..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Delivered By */}
              <FormField
                control={form.control}
                name="deliveredBy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Dikirim Oleh</FormLabel>
                    <FormControl>
                      <Input placeholder="Nama pengirim..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Received By */}
              <FormField
                control={form.control}
                name="receivedBy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Diterima Oleh</FormLabel>
                    <FormControl>
                      <Input placeholder="Nama penerima..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Delivery Condition */}
            <FormField
              control={form.control}
              name="deliveryCondition"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kondisi Pengiriman</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih kondisi pengiriman..." />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="good">Baik</SelectItem>
                      <SelectItem value="damaged">Rusak</SelectItem>
                      <SelectItem value="partial">Sebagian Rusak</SelectItem>
                      <SelectItem value="incomplete">Tidak Lengkap</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Items Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Item Penerimaan
              </CardTitle>
              <Button type="button" onClick={handleAddItem} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Tambah Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {fields.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Belum ada item. Klik "Tambah Item" untuk menambahkan item penerimaan.
              </div>
            ) : (
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produk</TableHead>
                      <TableHead>Batch</TableHead>
                      <TableHead>Exp. Date</TableHead>
                      <TableHead>Qty Diterima</TableHead>
                      <TableHead>Harga</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <GoodsReceiptItemRow
                        key={field.id}
                        index={index}
                        form={form}
                        onRemove={() => handleRemoveItem(index)}
                      />
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}

            {/* Total */}
            {fields.length > 0 && (
              <div className="flex justify-end mt-4 pt-4 border-t">
                <div className="text-right">
                  <div className="text-sm text-muted-foreground">Total Penerimaan</div>
                  <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notes Section */}
        <Card>
          <CardHeader>
            <CardTitle>Catatan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Umum</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan tambahan tentang penerimaan barang..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="internalNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan Internal</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan internal untuk tim..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Catatan ini hanya untuk internal dan tidak akan terlihat di dokumen eksternal
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button type="submit" disabled={isSubmitting} size="lg">
            {isSubmitting ? 'Menyimpan...' : mode === 'create' ? 'Buat Penerimaan' : 'Simpan Perubahan'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Separate component for item row to avoid re-rendering issues
interface GoodsReceiptItemRowProps {
  index: number;
  form: any;
  onRemove: () => void;
}

function GoodsReceiptItemRow({ index, form, onRemove }: GoodsReceiptItemRowProps) {
  const quantityReceived = form.watch(`items.${index}.quantityReceived`);
  const unitPrice = form.watch(`items.${index}.unitPrice`);
  const totalPrice = quantityReceived * unitPrice;

  return (
    <TableRow>
      <TableCell className="min-w-[200px]">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <ProductSelector
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="Pilih produk..."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.batchNumber`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Batch..." {...field} />
              </FormControl>
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.expiryDate`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input type="date" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.quantityReceived`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="number"
                  min="0"
                  step="1"
                  {...field}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </TableCell>
      <TableCell>
        <div className="font-medium">
          {formatCurrency(totalPrice)}
        </div>
      </TableCell>
      <TableCell>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
}
